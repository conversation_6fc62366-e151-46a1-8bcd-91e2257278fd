'use client';

import { useEffect, useRef } from 'react';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  type: 'circle' | 'triangle' | 'square' | 'diamond' | 'heart';
  rotation: number;
  rotationSpeed: number;
  life: number;
  maxLife: number;
}

const FlowingParticles = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Mouse tracking
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouseRef.current = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
    };

    canvas.addEventListener('mousemove', handleMouseMove);

    // Particle colors (VIP theme)
    const colors = [
      'rgba(59, 130, 246, 0.6)',   // Blue
      'rgba(147, 51, 234, 0.6)',   // Purple
      'rgba(255, 193, 7, 0.6)',    // Gold
      'rgba(239, 68, 68, 0.6)',    // Red
      'rgba(255, 215, 0, 0.6)',    // Yellow
      'rgba(16, 185, 129, 0.5)',   // Emerald
      'rgba(236, 72, 153, 0.5)',   // Pink
    ];

    const particleTypes: Array<'circle' | 'triangle' | 'square' | 'diamond' | 'heart'> = ['circle', 'triangle', 'square', 'diamond', 'heart', 'heart', 'heart']; // More hearts

    // Create particle
    const createParticle = (): Particle => {
      const side = Math.random() < 0.5 ? 'left' : 'top';
      let x, y, vx, vy;

      if (side === 'left') {
        x = -20;
        y = Math.random() * canvas.height;
        vx = Math.random() * 2 + 0.5;
        vy = (Math.random() - 0.5) * 1;
      } else {
        x = Math.random() * canvas.width;
        y = -20;
        vx = (Math.random() - 0.5) * 1;
        vy = Math.random() * 2 + 0.5;
      }

      return {
        x,
        y,
        vx,
        vy,
        size: Math.random() * 8 + 3,
        opacity: Math.random() * 0.8 + 0.2,
        color: colors[Math.floor(Math.random() * colors.length)],
        type: particleTypes[Math.floor(Math.random() * particleTypes.length)],
        rotation: Math.random() * Math.PI * 2,
        rotationSpeed: (Math.random() - 0.5) * 0.02,
        life: 0,
        maxLife: Math.random() * 300 + 200
      };
    };

    // Initialize particles
    const initParticles = () => {
      particlesRef.current = [];
      for (let i = 0; i < 50; i++) {
        particlesRef.current.push(createParticle());
      }
    };

    initParticles();

    // Draw different shapes
    const drawParticle = (ctx: CanvasRenderingContext2D, particle: Particle) => {
      ctx.save();
      ctx.translate(particle.x, particle.y);
      ctx.rotate(particle.rotation);
      ctx.globalAlpha = particle.opacity * (1 - particle.life / particle.maxLife);

      const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, particle.size);
      gradient.addColorStop(0, particle.color);
      gradient.addColorStop(1, particle.color.replace(/[\d.]+\)$/g, '0)'));

      ctx.fillStyle = gradient;
      ctx.strokeStyle = particle.color;
      ctx.lineWidth = 1;

      const size = particle.size;

      switch (particle.type) {
        case 'circle':
          ctx.beginPath();
          ctx.arc(0, 0, size, 0, Math.PI * 2);
          ctx.fill();
          break;

        case 'triangle':
          ctx.beginPath();
          ctx.moveTo(0, -size);
          ctx.lineTo(-size * 0.866, size * 0.5);
          ctx.lineTo(size * 0.866, size * 0.5);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          break;

        case 'square':
          ctx.fillRect(-size, -size, size * 2, size * 2);
          ctx.strokeRect(-size, -size, size * 2, size * 2);
          break;

        case 'diamond':
          ctx.beginPath();
          ctx.moveTo(0, -size);
          ctx.lineTo(size, 0);
          ctx.lineTo(0, size);
          ctx.lineTo(-size, 0);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          break;

        case 'heart':
          // Draw heart shape
          ctx.beginPath();
          ctx.moveTo(0, size * 0.3);

          // Left side of heart
          ctx.bezierCurveTo(-size * 0.8, -size * 0.3, -size * 0.8, size * 0.1, -size * 0.4, size * 0.1);
          ctx.bezierCurveTo(-size * 0.2, size * 0.1, 0, size * 0.4, 0, size * 0.8);

          // Right side of heart
          ctx.bezierCurveTo(0, size * 0.4, size * 0.2, size * 0.1, size * 0.4, size * 0.1);
          ctx.bezierCurveTo(size * 0.8, size * 0.1, size * 0.8, -size * 0.3, 0, size * 0.3);

          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          break;
      }

      ctx.restore();
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const particles = particlesRef.current;

      // Update particles
      for (let i = particles.length - 1; i >= 0; i--) {
        const particle = particles[i];

        // Mouse interaction
        const dx = mouseRef.current.x - particle.x;
        const dy = mouseRef.current.y - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          const force = (100 - distance) / 100;
          particle.vx += (dx / distance) * force * 0.01;
          particle.vy += (dy / distance) * force * 0.01;
        }

        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.rotation += particle.rotationSpeed;
        particle.life++;

        // Add some drift
        particle.vx += (Math.random() - 0.5) * 0.01;
        particle.vy += (Math.random() - 0.5) * 0.01;

        // Limit velocity
        const maxVel = 3;
        const vel = Math.sqrt(particle.vx * particle.vx + particle.vy * particle.vy);
        if (vel > maxVel) {
          particle.vx = (particle.vx / vel) * maxVel;
          particle.vy = (particle.vy / vel) * maxVel;
        }

        // Remove particles that are off screen or too old
        if (
          particle.x > canvas.width + 50 ||
          particle.y > canvas.height + 50 ||
          particle.x < -50 ||
          particle.y < -50 ||
          particle.life > particle.maxLife
        ) {
          particles.splice(i, 1);
        } else {
          drawParticle(ctx, particle);
        }
      }

      // Add new particles
      if (particles.length < 80 && Math.random() < 0.3) {
        particles.push(createParticle());
      }

      // Draw connecting lines between nearby particles
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 1;
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 120) {
            const opacity = (120 - distance) / 120 * 0.2;
            ctx.globalAlpha = opacity;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
            ctx.globalAlpha = 1;
          }
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      canvas.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default FlowingParticles;
