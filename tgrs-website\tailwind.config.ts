import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          50: "var(--primary-50)",
          100: "var(--primary-100)",
          200: "var(--primary-200)",
          300: "var(--primary-300)",
          400: "var(--primary-400)",
          500: "var(--primary-500)",
          600: "var(--primary-600)",
          700: "var(--primary-700)",
          800: "var(--primary-800)",
          900: "var(--primary-900)",
        },
        accent: {
          50: "var(--accent-50)",
          100: "var(--accent-100)",
          200: "var(--accent-200)",
          300: "var(--accent-300)",
          400: "var(--accent-400)",
          500: "var(--accent-500)",
          600: "var(--accent-600)",
          700: "var(--accent-700)",
          800: "var(--accent-800)",
          900: "var(--accent-900)",
        },
        neon: {
          orange: "var(--neon-orange)",
          "orange-light": "var(--neon-orange-light)",
          "orange-dark": "var(--neon-orange-dark)",
          red: "var(--neon-red)",
        },
      },
      fontFamily: {
        sans: ["Rajdhani", "sans-serif"],
        display: ["Orbitron", "sans-serif"],
      },
      animation: {
        "pulse-slow": "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "float": "float 6s ease-in-out infinite",
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'hero-pattern': "url('/assets/hero-bg.jpg')",
      },
      boxShadow: {
        'neon': '0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange)',
        'neon-strong': '0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange), 0 0 15px var(--neon-orange), 0 0 20px var(--neon-orange)',
        'neon-orange': '0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange)',
      },
      textShadow: {
        'neon': '0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange)',
      },
    },
  },
  plugins: [],
} satisfies Config;
