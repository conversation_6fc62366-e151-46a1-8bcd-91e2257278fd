{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/LivePlayerCount.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ServerData {\n  clients: number;\n  sv_maxclients: number;\n  svMaxclients: number;\n  hostname: string;\n}\n\ninterface ApiResponse {\n  Data: ServerData;\n}\n\nexport default function LivePlayerCount() {\n  const [playerCount, setPlayerCount] = useState<number | null>(null);\n  const [maxPlayers, setMaxPlayers] = useState<number>(48);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(false);\n\n  const fetchPlayerCount = async () => {\n    try {\n      const response = await fetch('https://servers-frontend.fivem.net/api/servers/single/o57lj7');\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch server data');\n      }\n\n      const data: ApiResponse = await response.json();\n\n      if (data.Data) {\n        setPlayerCount(data.Data.clients);\n        // Use svMaxclients as primary, fallback to sv_maxclients\n        setMaxPlayers(data.Data.svMaxclients || data.Data.sv_maxclients);\n        setError(false);\n      } else {\n        throw new Error('Invalid server data');\n      }\n    } catch (err) {\n      console.error('Error fetching player count:', err);\n      setError(true);\n      // Fallback to a default count if API fails\n      setPlayerCount(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // Initial fetch\n    fetchPlayerCount();\n\n    // Update every 30 seconds\n    const interval = setInterval(fetchPlayerCount, 30000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center space-x-2 text-gray-400\">\n        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-pulse\"></div>\n        <span className=\"text-sm\">Loading...</span>\n      </div>\n    );\n  }\n\n  if (error || playerCount === null) {\n    return (\n      <div className=\"flex items-center space-x-2 text-gray-500\">\n        <div className=\"w-2 h-2 bg-gray-500 rounded-full\"></div>\n        <span className=\"text-sm\">Server Offline</span>\n      </div>\n    );\n  }\n\n  const isServerFull = playerCount >= maxPlayers;\n  const isHighPopulation = playerCount >= maxPlayers * 0.8;\n\n  return (\n    <div className=\"flex items-center space-x-2 group cursor-pointer\" title={`${playerCount}/${maxPlayers} players online`}>\n      {/* Status indicator */}\n      <div className={`w-2 h-2 rounded-full transition-all duration-300 ${\n        isServerFull\n          ? 'bg-red-500 animate-pulse'\n          : isHighPopulation\n            ? 'bg-yellow-500 animate-pulse'\n            : 'bg-green-500 animate-pulse'\n      }`}></div>\n\n      {/* Player count */}\n      <div className=\"flex items-center space-x-1\">\n        <span className={`text-sm font-medium transition-colors duration-300 ${\n          isServerFull\n            ? 'text-red-400'\n            : isHighPopulation\n              ? 'text-yellow-400'\n              : 'text-green-400'\n        } group-hover:text-neon-orange`}>\n          {playerCount}\n        </span>\n        <span className=\"text-gray-400 text-sm\">/{maxPlayers}</span>\n      </div>\n\n      {/* Players icon */}\n      <i className=\"fas fa-users text-gray-400 text-sm group-hover:text-neon-orange transition-colors duration-300\"></i>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAee,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAoB,MAAM,SAAS,IAAI;YAE7C,IAAI,KAAK,IAAI,EAAE;gBACb,eAAe,KAAK,IAAI,CAAC,OAAO;gBAChC,yDAAyD;gBACzD,cAAc,KAAK,IAAI,CAAC,YAAY,IAAI,KAAK,IAAI,CAAC,aAAa;gBAC/D,SAAS;YACX,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;YACT,2CAA2C;YAC3C,eAAe;QACjB,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;QAChB;QAEA,0BAA0B;QAC1B,MAAM,WAAW,YAAY,kBAAkB;QAE/C,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,IAAI,SAAS,gBAAgB,MAAM;QACjC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,MAAM,eAAe,eAAe;IACpC,MAAM,mBAAmB,eAAe,aAAa;IAErD,qBACE,8OAAC;QAAI,WAAU;QAAmD,OAAO,GAAG,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC;;0BAEpH,8OAAC;gBAAI,WAAW,CAAC,iDAAiD,EAChE,eACI,6BACA,mBACE,gCACA,8BACN;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAW,CAAC,mDAAmD,EACnE,eACI,iBACA,mBACE,oBACA,iBACP,6BAA6B,CAAC;kCAC5B;;;;;;kCAEH,8OAAC;wBAAK,WAAU;;4BAAwB;4BAAE;;;;;;;;;;;;;0BAI5C,8OAAC;gBAAE,WAAU;;;;;;;;;;;;AAGnB"}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { usePathname } from \"next/navigation\";\nimport LivePlayerCount from \"./LivePlayerCount\";\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const pathname = usePathname();\n\n  // Helper function to check if a link is active\n  const isActive = (href: string) => {\n    if (href === \"/\") {\n      return pathname === \"/\";\n    }\n    return pathname.startsWith(href);\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <nav\n      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${\n        scrolled\n          ? \"bg-black/90 backdrop-blur-xl py-2 shadow-lg shadow-neon-orange/10\"\n          : \"bg-transparent py-4\"\n      }`}\n    >\n      {/* Subtle pattern overlay */}\n      <div className=\"absolute inset-0 pattern-dots opacity-20 pointer-events-none\"></div>\n\n      <div className=\"container mx-auto px-4 md:px-6 relative\">\n        <div className=\"flex items-center justify-between\">\n          {/* Enhanced Logo */}\n          <Link href=\"/\" className=\"flex items-center group\">\n            <div className=\"relative h-12 w-12 mr-3 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-neon-orange/20 to-neon-red/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300\"></div>\n              <Image\n                src=\"/assets/tgrs-logo.png\"\n                alt=\"TGRS Logo\"\n                width={48}\n                height={48}\n                className=\"w-full h-full object-contain relative z-10 drop-shadow-lg\"\n                priority\n              />\n            </div>\n            <span className=\"text-white font-display font-bold text-xl tracking-wider group-hover:text-neon-orange transition-all duration-300\">\n              TGRS\n            </span>\n          </Link>\n\n          {/* Enhanced Desktop Navigation - Centered */}\n          <div className=\"hidden md:flex items-center justify-center flex-1\">\n            {/* Navigation Links - Centered */}\n            <div className=\"flex items-center space-x-1\">\n              <Link\n                href=\"/\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Home\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  About\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/about\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/about\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/features\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Features\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/features\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/features\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/rules\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Rules\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/rules\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/rules\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/vip\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group overflow-hidden\"\n              >\n                {/* Purple heart with particles */}\n                <div className={`absolute inset-0 transition-all duration-500 ${\n                  isActive(\"/vip\") ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"\n                }`}>\n                  {/* Floating particles */}\n                  <div className=\"absolute top-1 left-2 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                  <div className=\"absolute top-3 right-3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                  <div className=\"absolute bottom-2 left-1 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                  <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                  <div className=\"absolute top-2 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  <div className=\"absolute bottom-3 right-1 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                </div>\n\n                {/* Purple gradient background */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/vip\") ? \"opacity-100 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n\n                {/* Glow effect */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm transition-all duration-500 scale-110 ${\n                  isActive(\"/vip\") ? \"opacity-60\" : \"opacity-0 group-hover:opacity-60\"\n                }`}></div>\n\n                <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                  isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                }`}>\n                  <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                    isActive(\"/vip\")\n                      ? \"text-pink-400 animate-pulse\"\n                      : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                  }`}></i>\n                  VIP\n                </span>\n\n              </Link>\n            </div>\n          </div>\n\n          {/* Right Side - Live Player Count & Play Now Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {/* Live Player Count */}\n            <LivePlayerCount />\n\n            {/* Enhanced Play Now Button */}\n            <Link\n              href=\"cfx.re/join/o57lj7\"\n              className=\"relative px-6 py-2.5 rounded-full font-medium transition-all duration-300 group overflow-hidden\"\n            >\n              {/* Animated background */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300 group-hover:scale-105\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n              {/* Glow effect */}\n              <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-md opacity-0 group-hover:opacity-50 transition-all duration-300 scale-110\"></div>\n\n              {/* Button text */}\n              <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                Play Now\n              </span>\n            </Link>\n          </div>\n\n          {/* Mobile Live Player Count & Menu Button */}\n          <div className=\"md:hidden flex items-center space-x-3\">\n            {/* Live Player Count */}\n            <LivePlayerCount />\n\n            {/* Enhanced Mobile Menu Button */}\n            <button\n              className=\"relative p-2 text-white focus:outline-none group transition-all duration-300\"\n              onClick={toggleMenu}\n            >\n            <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/20 to-neon-red/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n            <svg\n              className={`w-6 h-6 relative z-10 transition-all duration-300 ${isMenuOpen ? 'rotate-90 text-neon-orange' : 'rotate-0'}`}\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Enhanced Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden mt-6 relative animate-menu-slide-down\">\n            {/* Background with pattern */}\n            <div className=\"absolute inset-0 pattern-circuit opacity-20 rounded-xl\"></div>\n            <div className=\"relative bg-black/90 backdrop-blur-xl rounded-xl p-6 border border-neon-orange/40 shadow-lg shadow-neon-orange/20 overflow-hidden\">\n              {/* Particle effects background */}\n              <div className=\"absolute inset-0 opacity-30 pointer-events-none\">\n                <div className=\"absolute top-2 left-4 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                <div className=\"absolute top-4 right-6 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                <div className=\"absolute bottom-6 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                <div className=\"absolute bottom-3 right-4 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                <div className=\"absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                <div className=\"absolute top-8 right-2 w-0.5 h-0.5 bg-yellow-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n              </div>\n              <div className=\"flex flex-col space-y-4\">\n                <Link\n                  href=\"/\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Home\n                    {isActive(\"/\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/about\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/about\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    About\n                    {isActive(\"/about\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/features\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/features\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Features\n                    {isActive(\"/features\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/rules\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/rules\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Rules\n                    {isActive(\"/rules\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/vip\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg overflow-hidden\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Purple heart with particles */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-2 left-3 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-4 right-4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-3 left-2 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-2 right-3 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                    <div className=\"absolute bottom-4 right-2 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                  </div>\n\n                  {/* Purple gradient background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm opacity-0 group-hover:opacity-60 transition-all duration-500 scale-110\"></div>\n\n                  <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                    isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                  }`}>\n                    <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                      isActive(\"/vip\")\n                        ? \"text-pink-400 animate-pulse\"\n                        : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                    }`}></i>\n                    VIP\n                    {isActive(\"/vip\") && <span className=\"ml-2 text-purple-400 animate-pulse\">♥</span>}\n                  </span>\n                </Link>\n\n                {/* Divider */}\n                <div className=\"h-px bg-gradient-to-r from-transparent via-neon-orange/50 to-transparent my-2\"></div>\n\n                {/* Enhanced Mobile Play Now Button */}\n                <Link\n                  href=\"cfx.re/join/o57lj7\"\n                  className=\"relative px-6 py-3 rounded-full font-medium transition-all duration-300 group overflow-hidden text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Animated background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300\"></div>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-sm opacity-0 group-hover:opacity-60 transition-all duration-300\"></div>\n\n                  {/* Button text */}\n                  <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                    Play Now\n                  </span>\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,IAAI;gBACvB,YAAY;YACd,OAAO;gBACL,YAAY;YACd;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,2DAA2D,EACrE,WACI,sEACA,uBACJ;;0BAGF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;wCAAK,WAAU;kDAAoH;;;;;;;;;;;;0CAMtI,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;8DAAE;;;;;;gDAKH,SAAS,sBACR,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAA4E,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;sEACzH,8OAAC;4DAAI,WAAU;4DAAgF,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;;;;;;;8DAIjI,8OAAC;oDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,OAAO,yBAAyB,2DACzC;;;;;;;;;;;;sDAEJ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;8DAAE;;;;;;gDAKH,SAAS,2BACR,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAA4E,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;sEACzH,8OAAC;4DAAI,WAAU;4DAAgF,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;;;;;;;8DAIjI,8OAAC;oDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;sDAEJ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;8DAAE;;;;;;gDAKH,SAAS,8BACR,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAA4E,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;sEACzH,8OAAC;4DAAI,WAAU;4DAAgF,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;;;;;;;8DAIjI,8OAAC;oDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,eAAe,yBAAyB,2DACjD;;;;;;;;;;;;sDAEJ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;8DAAE;;;;;;gDAKH,SAAS,2BACR,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAA4E,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;sEACzH,8OAAC;4DAAI,WAAU;4DAAgF,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;;;;;;;8DAIjI,8OAAC;oDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;sDAEJ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAGV,8OAAC;oDAAI,WAAW,CAAC,6CAA6C,EAC5D,SAAS,UAAU,gBAAgB,qCACnC;;sEAEA,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAA0E,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;sEACvH,8OAAC;4DAAI,WAAU;4DAA+E,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;sEAC5H,8OAAC;4DAAI,WAAU;4DAAgF,OAAO;gEAAC,gBAAgB;4DAAM;;;;;;;;;;;;8DAI/H,8OAAC;oDAAI,WAAW,CAAC,sIAAsI,EACrJ,SAAS,UAAU,0BAA0B,2DAC7C;;;;;;8DAGF,8OAAC;oDAAI,WAAW,CAAC,6HAA6H,EAC5I,SAAS,UAAU,eAAe,oCAClC;;;;;;8DAEF,8OAAC;oDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;sEACA,8OAAC;4DAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAShB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,qIAAA,CAAA,UAAe;;;;;kDAGhB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAK,WAAU;0DAA6G;;;;;;;;;;;;;;;;;;0CAOjI,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,qIAAA,CAAA,UAAe;;;;;kDAGhB,8OAAC;wCACC,WAAU;wCACV,SAAS;;0DAEX,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDACC,WAAW,CAAC,kDAAkD,EAAE,aAAa,+BAA+B,YAAY;gDACxH,MAAK;gDACL,QAAO;gDACP,SAAQ;gDACR,OAAM;0DAEL,2BACC,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;yEAGJ,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASX,4BACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;gDAA4E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DACzH,8OAAC;gDAAI,WAAU;gDAAkF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAC/H,8OAAC;gDAAI,WAAU;gDAA+E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;kDAE9H,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,8OAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,OAAO,eAAe,qCAC/B;;;;;;kEACF,8OAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;;4DAAE;4DAED,SAAS,sBAAQ,8OAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAG5E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,8OAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,8OAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,8OAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,8OAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,eAAe,eAAe,qCACvC;;;;;;kEACF,8OAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;;4DAAE;4DAED,SAAS,8BAAgB,8OAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGpF,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,8OAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,8OAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,8OAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,8OAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,8OAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,8OAAC;wDAAI,WAAU;;;;;;kEAGf,8OAAC;wDAAI,WAAU;;;;;;kEAEf,8OAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,8OAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;4DAEP,SAAS,yBAAW,8OAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAK9E,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEAGf,8OAAC;wDAAI,WAAU;;;;;;kEAGf,8OAAC;wDAAK,WAAU;kEAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/I;uCAEe"}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/BouncingShapes.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface Shape {\n  id: number;\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  radius: number;\n  color: string;\n  type: 'circle' | 'triangle' | 'letter';\n  letter?: 'T' | 'G' | 'R' | 'S';\n  rotation: number;\n  rotationSpeed: number;\n}\n\nconst BouncingShapes = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number | undefined>(undefined);\n  const shapesRef = useRef<Shape[]>([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = canvas.offsetWidth;\n      canvas.height = canvas.offsetHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Initialize shapes\n    const initShapes = () => {\n      const shapes: Shape[] = [];\n      const colors = [\n        'rgba(212, 175, 55, 0.6)', // Golden\n        'rgba(205, 133, 63, 0.5)', // Bronze\n        'rgba(255, 215, 0, 0.4)',  // Gold\n        'rgba(184, 134, 11, 0.5)', // Dark gold\n        'rgba(251, 191, 36, 0.4)', // Amber\n        'rgba(255, 193, 7, 0.5)',  // Bright gold\n        'rgba(218, 165, 32, 0.4)', // Goldenrod\n        'rgba(255, 140, 0, 0.3)',  // Dark orange\n        'rgba(255, 165, 0, 0.4)',  // Orange\n      ];\n\n      // Add multiple TGRS letter sets for more population\n      const letters: ('T' | 'G' | 'R' | 'S')[] = ['T', 'G', 'R', 'S', 'T', 'G', 'R', 'S'];\n      const letterColors = [\n        'rgba(255, 215, 0, 0.8)',  // Bright gold for letters\n        'rgba(255, 193, 7, 0.8)',  // Golden\n        'rgba(255, 140, 0, 0.7)',  // Orange gold\n      ];\n\n      letters.forEach((letter, index) => {\n        shapes.push({\n          id: index,\n          x: Math.random() * (canvas.width - 100) + 50,\n          y: Math.random() * (canvas.height - 100) + 50,\n          vx: (Math.random() - 0.5) * 1.2,\n          vy: (Math.random() - 0.5) * 1.2,\n          radius: 22, // Larger size for letters to stand out\n          color: letterColors[Math.floor(Math.random() * letterColors.length)],\n          type: 'letter',\n          letter: letter,\n          rotation: Math.random() * Math.PI * 2,\n          rotationSpeed: (Math.random() - 0.5) * 0.02, // Random rotation speed\n        });\n      });\n\n      // Add circles and triangles\n      for (let i = 8; i < 16; i++) {\n        const shapeType = ['circle', 'triangle'][Math.floor(Math.random() * 2)] as 'circle' | 'triangle';\n        shapes.push({\n          id: i,\n          x: Math.random() * (canvas.width - 60) + 30,\n          y: Math.random() * (canvas.height - 60) + 30,\n          vx: (Math.random() - 0.5) * 2,\n          vy: (Math.random() - 0.5) * 2,\n          radius: Math.random() * 10 + 5,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          type: shapeType,\n          rotation: Math.random() * Math.PI * 2,\n          rotationSpeed: (Math.random() - 0.5) * 0.03,\n        });\n      }\n      shapesRef.current = shapes;\n    };\n\n    initShapes();\n\n    // Collision detection between two shapes\n    const checkCollision = (shape1: Shape, shape2: Shape) => {\n      const dx = shape1.x - shape2.x;\n      const dy = shape1.y - shape2.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      return distance < shape1.radius + shape2.radius;\n    };\n\n    // Handle collision response\n    const handleCollision = (shape1: Shape, shape2: Shape) => {\n      const dx = shape1.x - shape2.x;\n      const dy = shape1.y - shape2.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n\n      if (distance === 0) return; // Prevent division by zero\n\n      // Normalize collision vector\n      const nx = dx / distance;\n      const ny = dy / distance;\n\n      // Relative velocity\n      const dvx = shape1.vx - shape2.vx;\n      const dvy = shape1.vy - shape2.vy;\n\n      // Relative velocity in collision normal direction\n      const dvn = dvx * nx + dvy * ny;\n\n      // Do not resolve if velocities are separating\n      if (dvn > 0) return;\n\n      // Collision impulse\n      const impulse = 2 * dvn / 2; // Assuming equal mass\n\n      // Update velocities\n      shape1.vx -= impulse * nx;\n      shape1.vy -= impulse * ny;\n      shape2.vx += impulse * nx;\n      shape2.vy += impulse * ny;\n\n      // Add rotation on collision\n      shape1.rotationSpeed += (Math.random() - 0.5) * 0.02;\n      shape2.rotationSpeed += (Math.random() - 0.5) * 0.02;\n\n      // Separate overlapping shapes\n      const overlap = shape1.radius + shape2.radius - distance;\n      const separationX = (overlap / 2) * nx;\n      const separationY = (overlap / 2) * ny;\n\n      shape1.x += separationX;\n      shape1.y += separationY;\n      shape2.x -= separationX;\n      shape2.y -= separationY;\n    };\n\n    // Draw shape based on type\n    const drawShape = (ctx: CanvasRenderingContext2D, shape: Shape) => {\n      ctx.fillStyle = shape.color;\n      ctx.strokeStyle = shape.color.replace(/0\\.\\d+/, '0.8');\n      ctx.lineWidth = 1;\n\n      switch (shape.type) {\n        case 'circle':\n          ctx.beginPath();\n          ctx.arc(shape.x, shape.y, shape.radius, 0, Math.PI * 2);\n          ctx.fill();\n          ctx.stroke();\n          break;\n\n        case 'triangle':\n          ctx.save();\n          ctx.translate(shape.x, shape.y);\n          ctx.rotate(shape.rotation);\n          ctx.beginPath();\n          ctx.moveTo(0, -shape.radius);\n          ctx.lineTo(-shape.radius * 0.866, shape.radius * 0.5);\n          ctx.lineTo(shape.radius * 0.866, shape.radius * 0.5);\n          ctx.closePath();\n          ctx.fill();\n          ctx.stroke();\n          ctx.restore();\n          break;\n\n        case 'letter':\n          ctx.save();\n          ctx.translate(shape.x, shape.y);\n          ctx.rotate(shape.rotation);\n\n          // Add glow effect for letters\n          ctx.shadowColor = shape.color;\n          ctx.shadowBlur = 8;\n          ctx.shadowOffsetX = 0;\n          ctx.shadowOffsetY = 0;\n\n          ctx.font = `bold ${shape.radius * 1.3}px Arial`;\n          ctx.textAlign = 'center';\n          ctx.textBaseline = 'middle';\n\n          // Draw letter with enhanced styling\n          ctx.fillStyle = shape.color;\n          ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';\n          ctx.lineWidth = 1;\n          ctx.fillText(shape.letter || '', 0, 0);\n          ctx.strokeText(shape.letter || '', 0, 0);\n\n          // Reset shadow\n          ctx.shadowBlur = 0;\n          ctx.restore();\n          break;\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      const shapes = shapesRef.current;\n\n      // Update positions\n      shapes.forEach(shape => {\n        shape.x += shape.vx;\n        shape.y += shape.vy;\n\n        // Update rotation\n        shape.rotation += shape.rotationSpeed;\n\n        // Bounce off walls with minimal energy loss\n        if (shape.x - shape.radius <= 0 || shape.x + shape.radius >= canvas.width) {\n          shape.vx *= -0.98; // Minimal energy loss\n          shape.x = Math.max(shape.radius, Math.min(canvas.width - shape.radius, shape.x));\n          // Add rotation on wall bounce\n          shape.rotationSpeed += (Math.random() - 0.5) * 0.015;\n        }\n        if (shape.y - shape.radius <= 0 || shape.y + shape.radius >= canvas.height) {\n          shape.vy *= -0.98; // Minimal energy loss\n          shape.y = Math.max(shape.radius, Math.min(canvas.height - shape.radius, shape.y));\n          // Add rotation on wall bounce\n          shape.rotationSpeed += (Math.random() - 0.5) * 0.015;\n        }\n\n        // Add minimal friction to prevent infinite acceleration\n        shape.vx *= 0.9995;\n        shape.vy *= 0.9995;\n\n        // Maintain minimum and maximum speed\n        const maxSpeed = 3;\n        const minSpeed = 0.5;\n        const speed = Math.sqrt(shape.vx * shape.vx + shape.vy * shape.vy);\n\n        if (speed > maxSpeed) {\n          shape.vx = (shape.vx / speed) * maxSpeed;\n          shape.vy = (shape.vy / speed) * maxSpeed;\n        } else if (speed < minSpeed && speed > 0) {\n          // Give a small energy boost if moving too slowly\n          const boost = minSpeed / speed;\n          shape.vx *= boost;\n          shape.vy *= boost;\n        } else if (speed === 0) {\n          // If completely stopped, give random velocity\n          shape.vx = (Math.random() - 0.5) * 2;\n          shape.vy = (Math.random() - 0.5) * 2;\n        }\n      });\n\n      // Check collisions between all shapes\n      for (let i = 0; i < shapes.length; i++) {\n        for (let j = i + 1; j < shapes.length; j++) {\n          if (checkCollision(shapes[i], shapes[j])) {\n            handleCollision(shapes[i], shapes[j]);\n          }\n        }\n      }\n\n      // Draw all shapes\n      shapes.forEach(shape => drawShape(ctx, shape));\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 w-full h-full pointer-events-none\"\n      style={{ zIndex: 1 }}\n    />\n  );\n};\n\nexport default BouncingShapes;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBA,MAAM,iBAAiB;IACrB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAChD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW,EAAE;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,kBAAkB;QAClB,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,WAAW;YACjC,OAAO,MAAM,GAAG,OAAO,YAAY;QACrC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,oBAAoB;QACpB,MAAM,aAAa;YACjB,MAAM,SAAkB,EAAE;YAC1B,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,oDAAoD;YACpD,MAAM,UAAqC;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;aAAI;YACnF,MAAM,eAAe;gBACnB;gBACA;gBACA;aACD;YAED,QAAQ,OAAO,CAAC,CAAC,QAAQ;gBACvB,OAAO,IAAI,CAAC;oBACV,IAAI;oBACJ,GAAG,KAAK,MAAM,KAAK,CAAC,OAAO,KAAK,GAAG,GAAG,IAAI;oBAC1C,GAAG,KAAK,MAAM,KAAK,CAAC,OAAO,MAAM,GAAG,GAAG,IAAI;oBAC3C,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,QAAQ;oBACR,OAAO,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;oBACpE,MAAM;oBACN,QAAQ;oBACR,UAAU,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;oBACpC,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACzC;YACF;YAEA,4BAA4B;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,MAAM,YAAY;oBAAC;oBAAU;iBAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBACvE,OAAO,IAAI,CAAC;oBACV,IAAI;oBACJ,GAAG,KAAK,MAAM,KAAK,CAAC,OAAO,KAAK,GAAG,EAAE,IAAI;oBACzC,GAAG,KAAK,MAAM,KAAK,CAAC,OAAO,MAAM,GAAG,EAAE,IAAI;oBAC1C,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,QAAQ,KAAK,MAAM,KAAK,KAAK;oBAC7B,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;oBACxD,MAAM;oBACN,UAAU,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;oBACpC,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACzC;YACF;YACA,UAAU,OAAO,GAAG;QACtB;QAEA;QAEA,yCAAyC;QACzC,MAAM,iBAAiB,CAAC,QAAe;YACrC,MAAM,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC;YAC9B,MAAM,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC;YAC9B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;YAC1C,OAAO,WAAW,OAAO,MAAM,GAAG,OAAO,MAAM;QACjD;QAEA,4BAA4B;QAC5B,MAAM,kBAAkB,CAAC,QAAe;YACtC,MAAM,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC;YAC9B,MAAM,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC;YAC9B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;YAE1C,IAAI,aAAa,GAAG,QAAQ,2BAA2B;YAEvD,6BAA6B;YAC7B,MAAM,KAAK,KAAK;YAChB,MAAM,KAAK,KAAK;YAEhB,oBAAoB;YACpB,MAAM,MAAM,OAAO,EAAE,GAAG,OAAO,EAAE;YACjC,MAAM,MAAM,OAAO,EAAE,GAAG,OAAO,EAAE;YAEjC,kDAAkD;YAClD,MAAM,MAAM,MAAM,KAAK,MAAM;YAE7B,8CAA8C;YAC9C,IAAI,MAAM,GAAG;YAEb,oBAAoB;YACpB,MAAM,UAAU,IAAI,MAAM,GAAG,sBAAsB;YAEnD,oBAAoB;YACpB,OAAO,EAAE,IAAI,UAAU;YACvB,OAAO,EAAE,IAAI,UAAU;YACvB,OAAO,EAAE,IAAI,UAAU;YACvB,OAAO,EAAE,IAAI,UAAU;YAEvB,4BAA4B;YAC5B,OAAO,aAAa,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAChD,OAAO,aAAa,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAEhD,8BAA8B;YAC9B,MAAM,UAAU,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG;YAChD,MAAM,cAAc,AAAC,UAAU,IAAK;YACpC,MAAM,cAAc,AAAC,UAAU,IAAK;YAEpC,OAAO,CAAC,IAAI;YACZ,OAAO,CAAC,IAAI;YACZ,OAAO,CAAC,IAAI;YACZ,OAAO,CAAC,IAAI;QACd;QAEA,2BAA2B;QAC3B,MAAM,YAAY,CAAC,KAA+B;YAChD,IAAI,SAAS,GAAG,MAAM,KAAK;YAC3B,IAAI,WAAW,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,UAAU;YAChD,IAAI,SAAS,GAAG;YAEhB,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG;oBACrD,IAAI,IAAI;oBACR,IAAI,MAAM;oBACV;gBAEF,KAAK;oBACH,IAAI,IAAI;oBACR,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;oBAC9B,IAAI,MAAM,CAAC,MAAM,QAAQ;oBACzB,IAAI,SAAS;oBACb,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,MAAM;oBAC3B,IAAI,MAAM,CAAC,CAAC,MAAM,MAAM,GAAG,OAAO,MAAM,MAAM,GAAG;oBACjD,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,MAAM,MAAM,GAAG;oBAChD,IAAI,SAAS;oBACb,IAAI,IAAI;oBACR,IAAI,MAAM;oBACV,IAAI,OAAO;oBACX;gBAEF,KAAK;oBACH,IAAI,IAAI;oBACR,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;oBAC9B,IAAI,MAAM,CAAC,MAAM,QAAQ;oBAEzB,8BAA8B;oBAC9B,IAAI,WAAW,GAAG,MAAM,KAAK;oBAC7B,IAAI,UAAU,GAAG;oBACjB,IAAI,aAAa,GAAG;oBACpB,IAAI,aAAa,GAAG;oBAEpB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC;oBAC/C,IAAI,SAAS,GAAG;oBAChB,IAAI,YAAY,GAAG;oBAEnB,oCAAoC;oBACpC,IAAI,SAAS,GAAG,MAAM,KAAK;oBAC3B,IAAI,WAAW,GAAG;oBAClB,IAAI,SAAS,GAAG;oBAChB,IAAI,QAAQ,CAAC,MAAM,MAAM,IAAI,IAAI,GAAG;oBACpC,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,IAAI,GAAG;oBAEtC,eAAe;oBACf,IAAI,UAAU,GAAG;oBACjB,IAAI,OAAO;oBACX;YACJ;QACF;QAEA,iBAAiB;QACjB,MAAM,UAAU;YACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C,MAAM,SAAS,UAAU,OAAO;YAEhC,mBAAmB;YACnB,OAAO,OAAO,CAAC,CAAA;gBACb,MAAM,CAAC,IAAI,MAAM,EAAE;gBACnB,MAAM,CAAC,IAAI,MAAM,EAAE;gBAEnB,kBAAkB;gBAClB,MAAM,QAAQ,IAAI,MAAM,aAAa;gBAErC,4CAA4C;gBAC5C,IAAI,MAAM,CAAC,GAAG,MAAM,MAAM,IAAI,KAAK,MAAM,CAAC,GAAG,MAAM,MAAM,IAAI,OAAO,KAAK,EAAE;oBACzE,MAAM,EAAE,IAAI,CAAC,MAAM,sBAAsB;oBACzC,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,MAAM,MAAM,EAAE,MAAM,CAAC;oBAC9E,8BAA8B;oBAC9B,MAAM,aAAa,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACjD;gBACA,IAAI,MAAM,CAAC,GAAG,MAAM,MAAM,IAAI,KAAK,MAAM,CAAC,GAAG,MAAM,MAAM,IAAI,OAAO,MAAM,EAAE;oBAC1E,MAAM,EAAE,IAAI,CAAC,MAAM,sBAAsB;oBACzC,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,KAAK,GAAG,CAAC,OAAO,MAAM,GAAG,MAAM,MAAM,EAAE,MAAM,CAAC;oBAC/E,8BAA8B;oBAC9B,MAAM,aAAa,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACjD;gBAEA,wDAAwD;gBACxD,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;gBAEZ,qCAAqC;gBACrC,MAAM,WAAW;gBACjB,MAAM,WAAW;gBACjB,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;gBAEjE,IAAI,QAAQ,UAAU;oBACpB,MAAM,EAAE,GAAG,AAAC,MAAM,EAAE,GAAG,QAAS;oBAChC,MAAM,EAAE,GAAG,AAAC,MAAM,EAAE,GAAG,QAAS;gBAClC,OAAO,IAAI,QAAQ,YAAY,QAAQ,GAAG;oBACxC,iDAAiD;oBACjD,MAAM,QAAQ,WAAW;oBACzB,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;gBACd,OAAO,IAAI,UAAU,GAAG;oBACtB,8CAA8C;oBAC9C,MAAM,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACnC,MAAM,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACrC;YACF;YAEA,sCAAsC;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBAC1C,IAAI,eAAe,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,GAAG;wBACxC,gBAAgB,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;oBACtC;gBACF;YACF;YAEA,kBAAkB;YAClB,OAAO,OAAO,CAAC,CAAA,QAAS,UAAU,KAAK;YAEvC,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB;uCAEe"}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}