import React from "react";
import Link from "next/link";

interface ButtonProps {
  children: React.ReactNode;
  href?: string;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  className?: string;
  onClick?: () => void;
}

const Button = ({
  children,
  href,
  variant = "primary",
  size = "md",
  className = "",
  onClick,
}: ButtonProps) => {
  const baseStyles = "inline-flex items-center justify-center font-medium transition-all duration-300 rounded-full";

  const variantStyles = {
    primary: "bg-gradient-to-r from-neon-orange to-neon-red text-white hover:shadow-neon",
    secondary: "bg-neon-orange text-white hover:bg-neon-orange/90 hover:shadow-neon-orange",
    outline: "border border-neon-orange text-neon-orange hover:bg-neon-orange/10",
    ghost: "text-white hover:text-neon-orange hover:bg-white/5",
  };

  const sizeStyles = {
    sm: "text-xs px-3 py-1.5",
    md: "text-sm px-4 py-2",
    lg: "text-base px-6 py-3",
  };

  const styles = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;

  if (href) {
    // Check if it's an external link
    const isExternal = href.startsWith('http') || href.startsWith('https');

    if (isExternal) {
      return (
        <a href={href} className={styles} target="_blank" rel="noopener noreferrer">
          {children}
        </a>
      );
    }

    return (
      <Link href={href} className={styles}>
        {children}
      </Link>
    );
  }

  return (
    <button className={styles} onClick={onClick}>
      {children}
    </button>
  );
};

export default Button;
