'use client';

import { useEffect, useRef } from 'react';

interface CashBill {
  x: number;
  y: number;
  vx: number;
  vy: number;
  rotation: number;
  rotationSpeed: number;
  size: number;
  opacity: number;
  type: 'dollar' | 'euro' | 'rupee' | 'bitcoin';
  color: string;
  life: number;
  maxLife: number;
  swayOffset: number;
  swaySpeed: number;
}

const CashFlyingAnimation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const cashBillsRef = useRef<CashBill[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Mouse tracking for interaction
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouseRef.current = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
    };

    canvas.addEventListener('mousemove', handleMouseMove);

    // Cash bill colors (premium theme)
    const cashColors = [
      '#10B981', // Emerald (Dollar)
      '#3B82F6', // Blue (Euro)
      '#F59E0B', // Amber (Rupee)
      '#EF4444', // Red (Yuan)
      '#8B5CF6', // Purple (Pound)
      '#F97316', // Orange (Bitcoin)
      '#06B6D4', // Cyan (Yen)
    ];

    const cashTypes: Array<'dollar' | 'euro' | 'rupee' | 'bitcoin'> = ['dollar', 'euro', 'rupee', 'bitcoin'];

    // Create cash bill
    const createCashBill = (): CashBill => {
      const spawnSide = Math.random();
      let x, y, vx, vy;

      if (spawnSide < 0.3) {
        // From left
        x = -50;
        y = Math.random() * canvas.height;
        vx = Math.random() * 3 + 1;
        vy = (Math.random() - 0.5) * 2;
      } else if (spawnSide < 0.6) {
        // From top
        x = Math.random() * canvas.width;
        y = -50;
        vx = (Math.random() - 0.5) * 2;
        vy = Math.random() * 3 + 1;
      } else {
        // From right (cash blowing in)
        x = canvas.width + 50;
        y = Math.random() * canvas.height;
        vx = -(Math.random() * 3 + 1);
        vy = (Math.random() - 0.5) * 2;
      }

      return {
        x,
        y,
        vx,
        vy,
        rotation: Math.random() * Math.PI * 2,
        rotationSpeed: (Math.random() - 0.5) * 0.05,
        size: Math.random() * 25 + 20,
        opacity: Math.random() * 0.9 + 0.4,
        type: cashTypes[Math.floor(Math.random() * cashTypes.length)],
        color: cashColors[Math.floor(Math.random() * cashColors.length)],
        life: 0,
        maxLife: Math.random() * 400 + 300,
        swayOffset: Math.random() * Math.PI * 2,
        swaySpeed: Math.random() * 0.02 + 0.01
      };
    };

    // Initialize cash bills
    const initCashBills = () => {
      cashBillsRef.current = [];
      for (let i = 0; i < 50; i++) {
        cashBillsRef.current.push(createCashBill());
      }
    };

    initCashBills();

    // Draw cash bill
    const drawCashBill = (ctx: CanvasRenderingContext2D, bill: CashBill) => {
      ctx.save();
      ctx.translate(bill.x, bill.y);
      ctx.rotate(bill.rotation);
      ctx.globalAlpha = bill.opacity * (1 - bill.life / bill.maxLife);

      const width = bill.size * 1.8;
      const height = bill.size * 0.8;

      // Create gradient for cash bill
      const gradient = ctx.createLinearGradient(-width/2, -height/2, width/2, height/2);
      gradient.addColorStop(0, bill.color);
      gradient.addColorStop(0.5, '#ffffff40');
      gradient.addColorStop(1, bill.color);

      // Draw cash bill background
      ctx.fillStyle = gradient;
      ctx.strokeStyle = bill.color;
      ctx.lineWidth = 2;

      // Rounded rectangle for bill
      const radius = 4;
      ctx.beginPath();
      ctx.roundRect(-width/2, -height/2, width, height, radius);
      ctx.fill();
      ctx.stroke();

      // Draw currency symbol
      ctx.fillStyle = '#ffffff';
      ctx.font = `bold ${bill.size * 0.6}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      let symbol = '$';
      switch (bill.type) {
        case 'euro': symbol = '€'; break;
        case 'rupee': symbol = '₹'; break;
        case 'bitcoin': symbol = '₿'; break;
        default: symbol = '$';
      }

      ctx.fillText(symbol, 0, 0);

      // Add shine effect
      const shineGradient = ctx.createLinearGradient(-width/2, -height/2, width/2, height/2);
      shineGradient.addColorStop(0, 'rgba(255,255,255,0)');
      shineGradient.addColorStop(0.5, 'rgba(255,255,255,0.3)');
      shineGradient.addColorStop(1, 'rgba(255,255,255,0)');

      ctx.fillStyle = shineGradient;
      ctx.beginPath();
      ctx.roundRect(-width/2, -height/2, width, height, radius);
      ctx.fill();

      ctx.restore();
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const bills = cashBillsRef.current;

      // Update cash bills
      for (let i = bills.length - 1; i >= 0; i--) {
        const bill = bills[i];

        // Mouse interaction - bills get attracted to mouse
        const dx = mouseRef.current.x - bill.x;
        const dy = mouseRef.current.y - bill.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 150) {
          const force = (150 - distance) / 150;
          bill.vx += (dx / distance) * force * 0.02;
          bill.vy += (dy / distance) * force * 0.02;
        }

        // Add floating/swaying motion
        bill.swayOffset += bill.swaySpeed;
        const swayX = Math.sin(bill.swayOffset) * 0.5;
        const swayY = Math.cos(bill.swayOffset * 0.7) * 0.3;

        // Update position with sway
        bill.x += bill.vx + swayX;
        bill.y += bill.vy + swayY;
        bill.rotation += bill.rotationSpeed;
        bill.life++;

        // Add some air resistance and gravity
        bill.vx *= 0.995;
        bill.vy += 0.01; // Slight gravity
        bill.vy *= 0.995;

        // Limit velocity
        const maxVel = 4;
        const vel = Math.sqrt(bill.vx * bill.vx + bill.vy * bill.vy);
        if (vel > maxVel) {
          bill.vx = (bill.vx / vel) * maxVel;
          bill.vy = (bill.vy / vel) * maxVel;
        }

        // Remove bills that are off screen or too old
        if (
          bill.x > canvas.width + 100 ||
          bill.y > canvas.height + 100 ||
          bill.x < -100 ||
          bill.y < -100 ||
          bill.life > bill.maxLife
        ) {
          bills.splice(i, 1);
        } else {
          drawCashBill(ctx, bill);
        }
      }

      // Add new cash bills
      if (bills.length < 80 && Math.random() < 0.6) {
        bills.push(createCashBill());
      }

      // Draw connecting lines between nearby bills (money flow effect)
      ctx.strokeStyle = 'rgba(16, 185, 129, 0.2)';
      ctx.lineWidth = 1.5;
      for (let i = 0; i < bills.length; i++) {
        for (let j = i + 1; j < bills.length; j++) {
          const dx = bills[i].x - bills[j].x;
          const dy = bills[i].y - bills[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 120) {
            const opacity = (120 - distance) / 120 * 0.25;
            ctx.globalAlpha = opacity;
            ctx.beginPath();
            ctx.moveTo(bills[i].x, bills[i].y);
            ctx.lineTo(bills[j].x, bills[j].y);
            ctx.stroke();
            ctx.globalAlpha = 1;
          }
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      canvas.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default CashFlyingAnimation;
