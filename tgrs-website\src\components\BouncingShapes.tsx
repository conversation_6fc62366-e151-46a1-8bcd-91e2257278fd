'use client';

import { useEffect, useRef } from 'react';

interface Shape {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  color: string;
  type: 'circle' | 'triangle' | 'letter';
  letter?: 'T' | 'G' | 'R' | 'S';
  rotation: number;
  rotationSpeed: number;
}

const BouncingShapes = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const shapesRef = useRef<Shape[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize shapes
    const initShapes = () => {
      const shapes: Shape[] = [];
      const colors = [
        'rgba(212, 175, 55, 0.6)', // Golden
        'rgba(205, 133, 63, 0.5)', // Bronze
        'rgba(255, 215, 0, 0.4)',  // Gold
        'rgba(184, 134, 11, 0.5)', // Dark gold
        'rgba(251, 191, 36, 0.4)', // Amber
        'rgba(255, 193, 7, 0.5)',  // Bright gold
        'rgba(218, 165, 32, 0.4)', // Goldenrod
        'rgba(255, 140, 0, 0.3)',  // Dark orange
        'rgba(255, 165, 0, 0.4)',  // Orange
      ];

      // Add multiple TGRS letter sets for more population
      const letters: ('T' | 'G' | 'R' | 'S')[] = ['T', 'G', 'R', 'S', 'T', 'G', 'R', 'S'];
      const letterColors = [
        'rgba(255, 215, 0, 0.8)',  // Bright gold for letters
        'rgba(255, 193, 7, 0.8)',  // Golden
        'rgba(255, 140, 0, 0.7)',  // Orange gold
      ];

      letters.forEach((letter, index) => {
        shapes.push({
          id: index,
          x: Math.random() * (canvas.width - 100) + 50,
          y: Math.random() * (canvas.height - 100) + 50,
          vx: (Math.random() - 0.5) * 1.2,
          vy: (Math.random() - 0.5) * 1.2,
          radius: 22, // Larger size for letters to stand out
          color: letterColors[Math.floor(Math.random() * letterColors.length)],
          type: 'letter',
          letter: letter,
          rotation: Math.random() * Math.PI * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.02, // Random rotation speed
        });
      });

      // Add circles and triangles
      for (let i = 8; i < 16; i++) {
        const shapeType = ['circle', 'triangle'][Math.floor(Math.random() * 2)] as 'circle' | 'triangle';
        shapes.push({
          id: i,
          x: Math.random() * (canvas.width - 60) + 30,
          y: Math.random() * (canvas.height - 60) + 30,
          vx: (Math.random() - 0.5) * 2,
          vy: (Math.random() - 0.5) * 2,
          radius: Math.random() * 10 + 5,
          color: colors[Math.floor(Math.random() * colors.length)],
          type: shapeType,
          rotation: Math.random() * Math.PI * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.03,
        });
      }
      shapesRef.current = shapes;
    };

    initShapes();

    // Collision detection between two shapes
    const checkCollision = (shape1: Shape, shape2: Shape) => {
      const dx = shape1.x - shape2.x;
      const dy = shape1.y - shape2.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      return distance < shape1.radius + shape2.radius;
    };

    // Handle collision response
    const handleCollision = (shape1: Shape, shape2: Shape) => {
      const dx = shape1.x - shape2.x;
      const dy = shape1.y - shape2.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance === 0) return; // Prevent division by zero

      // Normalize collision vector
      const nx = dx / distance;
      const ny = dy / distance;

      // Relative velocity
      const dvx = shape1.vx - shape2.vx;
      const dvy = shape1.vy - shape2.vy;

      // Relative velocity in collision normal direction
      const dvn = dvx * nx + dvy * ny;

      // Do not resolve if velocities are separating
      if (dvn > 0) return;

      // Collision impulse
      const impulse = 2 * dvn / 2; // Assuming equal mass

      // Update velocities
      shape1.vx -= impulse * nx;
      shape1.vy -= impulse * ny;
      shape2.vx += impulse * nx;
      shape2.vy += impulse * ny;

      // Add rotation on collision
      shape1.rotationSpeed += (Math.random() - 0.5) * 0.02;
      shape2.rotationSpeed += (Math.random() - 0.5) * 0.02;

      // Separate overlapping shapes
      const overlap = shape1.radius + shape2.radius - distance;
      const separationX = (overlap / 2) * nx;
      const separationY = (overlap / 2) * ny;

      shape1.x += separationX;
      shape1.y += separationY;
      shape2.x -= separationX;
      shape2.y -= separationY;
    };

    // Draw shape based on type
    const drawShape = (ctx: CanvasRenderingContext2D, shape: Shape) => {
      ctx.fillStyle = shape.color;
      ctx.strokeStyle = shape.color.replace(/0\.\d+/, '0.8');
      ctx.lineWidth = 1;

      switch (shape.type) {
        case 'circle':
          ctx.beginPath();
          ctx.arc(shape.x, shape.y, shape.radius, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
          break;

        case 'triangle':
          ctx.save();
          ctx.translate(shape.x, shape.y);
          ctx.rotate(shape.rotation);
          ctx.beginPath();
          ctx.moveTo(0, -shape.radius);
          ctx.lineTo(-shape.radius * 0.866, shape.radius * 0.5);
          ctx.lineTo(shape.radius * 0.866, shape.radius * 0.5);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          ctx.restore();
          break;

        case 'letter':
          ctx.save();
          ctx.translate(shape.x, shape.y);
          ctx.rotate(shape.rotation);

          // Add glow effect for letters
          ctx.shadowColor = shape.color;
          ctx.shadowBlur = 8;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;

          ctx.font = `bold ${shape.radius * 1.3}px Arial`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';

          // Draw letter with enhanced styling
          ctx.fillStyle = shape.color;
          ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
          ctx.lineWidth = 1;
          ctx.fillText(shape.letter || '', 0, 0);
          ctx.strokeText(shape.letter || '', 0, 0);

          // Reset shadow
          ctx.shadowBlur = 0;
          ctx.restore();
          break;
      }
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const shapes = shapesRef.current;

      // Update positions
      shapes.forEach(shape => {
        shape.x += shape.vx;
        shape.y += shape.vy;

        // Update rotation
        shape.rotation += shape.rotationSpeed;

        // Bounce off walls with minimal energy loss
        if (shape.x - shape.radius <= 0 || shape.x + shape.radius >= canvas.width) {
          shape.vx *= -0.98; // Minimal energy loss
          shape.x = Math.max(shape.radius, Math.min(canvas.width - shape.radius, shape.x));
          // Add rotation on wall bounce
          shape.rotationSpeed += (Math.random() - 0.5) * 0.015;
        }
        if (shape.y - shape.radius <= 0 || shape.y + shape.radius >= canvas.height) {
          shape.vy *= -0.98; // Minimal energy loss
          shape.y = Math.max(shape.radius, Math.min(canvas.height - shape.radius, shape.y));
          // Add rotation on wall bounce
          shape.rotationSpeed += (Math.random() - 0.5) * 0.015;
        }

        // Add minimal friction to prevent infinite acceleration
        shape.vx *= 0.9995;
        shape.vy *= 0.9995;

        // Maintain minimum and maximum speed
        const maxSpeed = 3;
        const minSpeed = 0.5;
        const speed = Math.sqrt(shape.vx * shape.vx + shape.vy * shape.vy);

        if (speed > maxSpeed) {
          shape.vx = (shape.vx / speed) * maxSpeed;
          shape.vy = (shape.vy / speed) * maxSpeed;
        } else if (speed < minSpeed && speed > 0) {
          // Give a small energy boost if moving too slowly
          const boost = minSpeed / speed;
          shape.vx *= boost;
          shape.vy *= boost;
        } else if (speed === 0) {
          // If completely stopped, give random velocity
          shape.vx = (Math.random() - 0.5) * 2;
          shape.vy = (Math.random() - 0.5) * 2;
        }
      });

      // Check collisions between all shapes
      for (let i = 0; i < shapes.length; i++) {
        for (let j = i + 1; j < shapes.length; j++) {
          if (checkCollision(shapes[i], shapes[j])) {
            handleCollision(shapes[i], shapes[j]);
          }
        }
      }

      // Draw all shapes
      shapes.forEach(shape => drawShape(ctx, shape));

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default BouncingShapes;
