'use client';

import Script from 'next/script';

export default function Analytics() {
  return (
    <>
      {/* Google Analytics */}
      {process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}', {
                page_title: document.title,
                page_location: window.location.href,
                custom_map: {
                  'custom_parameter_1': 'server_type',
                  'custom_parameter_2': 'user_language'
                }
              });

              // Track Telugu gaming events
              gtag('event', 'page_view', {
                'custom_parameter_1': 'telugu_fivem',
                'custom_parameter_2': 'telugu'
              });
            `}
          </Script>
        </>
      )}

      {/* Microsoft Clarity */}
      {process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID && (
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID}");
          `}
        </Script>
      )}

      {/* Facebook Pixel */}
      {process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID && (
        <Script id="facebook-pixel" strategy="afterInteractive">
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID}');
            fbq('track', 'PageView');

            // Track Telugu gaming interest
            fbq('trackCustom', 'TeluguGamingInterest', {
              server_type: 'fivem',
              language: 'telugu',
              community: 'tgrs'
            });
          `}
        </Script>
      )}

      {/* Hotjar - Disabled until HOTJAR_ID is configured */}
      {process.env.NEXT_PUBLIC_HOTJAR_ID && (
        <Script id="hotjar" strategy="afterInteractive">
          {`
            (function(h,o,t,j,a,r){
              h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
              h._hjSettings={hjid:${process.env.NEXT_PUBLIC_HOTJAR_ID},hjsv:6};
              a=o.getElementsByTagName('head')[0];
              r=o.createElement('script');r.async=1;
              r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
              a.appendChild(r);
            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
          `}
        </Script>
      )}

      {/* Custom Telugu Gaming Tracking */}
      <Script id="telugu-gaming-tracking" strategy="afterInteractive">
        {`
          // Track Telugu gaming specific events
          function trackTeluguGamingEvent(eventName, properties = {}) {
            // Google Analytics
            if (typeof gtag !== 'undefined') {
              gtag('event', eventName, {
                event_category: 'Telugu Gaming',
                event_label: 'TGRS',
                ...properties
              });
            }

            // Facebook Pixel
            if (typeof fbq !== 'undefined') {
              fbq('trackCustom', eventName, {
                server: 'tgrs',
                language: 'telugu',
                ...properties
              });
            }

            // Console log for debugging
            console.log('Telugu Gaming Event:', eventName, properties);
          }

          // Track page interactions
          document.addEventListener('DOMContentLoaded', function() {
            // Track Discord link clicks
            document.querySelectorAll('a[href*="discord.gg"]').forEach(link => {
              link.addEventListener('click', () => {
                trackTeluguGamingEvent('discord_click', {
                  source: 'website',
                  page: window.location.pathname
                });
              });
            });

            // Track VIP interest
            document.querySelectorAll('a[href*="/vip"]').forEach(link => {
              link.addEventListener('click', () => {
                trackTeluguGamingEvent('vip_interest', {
                  source: 'navigation',
                  page: window.location.pathname
                });
              });
            });

            // Track server join attempts
            document.querySelectorAll('a[href*="cfx.re"]').forEach(link => {
              link.addEventListener('click', () => {
                trackTeluguGamingEvent('server_join_attempt', {
                  source: 'website',
                  page: window.location.pathname
                });
              });
            });
          });

          // Track scroll depth for engagement
          let maxScroll = 0;
          window.addEventListener('scroll', () => {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
              maxScroll = scrollPercent;
              trackTeluguGamingEvent('scroll_depth', {
                depth: scrollPercent,
                page: window.location.pathname
              });
            }
          });
        `}
      </Script>
    </>
  );
}
