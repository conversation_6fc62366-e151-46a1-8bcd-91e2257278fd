import React from "react";

interface LogoPlaceholderProps {
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

const LogoPlaceholder = ({
  size = "md",
  className = "",
}: LogoPlaceholderProps) => {
  const sizeStyles = {
    sm: "h-8 w-8",
    md: "h-12 w-12",
    lg: "h-16 w-16",
    xl: "h-24 w-24",
  };

  return (
    <div className={`relative ${sizeStyles[size]} ${className}`}>
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red animate-pulse-slow"></div>
      <div className="absolute inset-1 rounded-full bg-black flex items-center justify-center text-white font-display font-bold">
        <span className={size === "sm" ? "text-sm" : size === "md" ? "text-lg" : size === "lg" ? "text-2xl" : "text-3xl"}>
          T
        </span>
      </div>
    </div>
  );
};

export default LogoPlaceholder;
