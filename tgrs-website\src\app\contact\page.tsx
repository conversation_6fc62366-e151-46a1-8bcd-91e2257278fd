"use client";

import React, { useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Button from "@/components/Button";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    discord: "",
    subject: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<null | "success" | "error">(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitStatus("success");

      // Reset form after successful submission
      setFormData({
        name: "",
        email: "",
        discord: "",
        subject: "",
        message: "",
      });

      // Reset status after 5 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 5000);
    }, 1500);
  };

  return (
    <>
      <Navbar />

      {/* Header */}
      <div className="pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black">
        <div className="absolute inset-0 pattern-circuit opacity-5"></div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg">
              <span className="text-neon-orange">CONTACT</span> US
            </h1>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Have questions or feedback? We'd love to hear from you!
            </p>
          </div>
        </div>
      </div>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* Contact Section */}
      <section className="py-20 relative bg-black/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="glass-enhanced rounded-xl p-6 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative pattern-circuit">
              <div className="absolute inset-0 bg-black/20 rounded-xl"></div>
              <div className="relative z-10">
                <h2 className="text-2xl font-display font-bold text-white mb-6">
                  Send Us a Message
                </h2>

                {submitStatus === "success" && (
                  <div className="mb-6 p-4 rounded-lg bg-green-500/20 border border-green-500/50 text-green-400">
                    Your message has been sent successfully! We'll get back to you soon.
                  </div>
                )}

                {submitStatus === "error" && (
                  <div className="mb-6 p-4 rounded-lg bg-red-500/20 border border-red-500/50 text-red-400">
                    There was an error sending your message. Please try again later.
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="name" className="block text-white mb-2">
                      Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors"
                      placeholder="Your name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-white mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors"
                      placeholder="Your email"
                    />
                  </div>
                </div>

                <div className="mb-6">
                  <label htmlFor="discord" className="block text-white mb-2">
                    Discord Username (optional)
                  </label>
                  <input
                    type="text"
                    id="discord"
                    name="discord"
                    value={formData.discord}
                    onChange={handleChange}
                    className="w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors"
                    placeholder="Your Discord username"
                  />
                </div>

                <div className="mb-6">
                  <label htmlFor="subject" className="block text-white mb-2">
                    Subject
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors"
                  >
                    <option value="" disabled>Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="support">Technical Support</option>
                    <option value="feedback">Feedback</option>
                    <option value="report">Report an Issue</option>
                    <option value="join">How to Join</option>
                  </select>
                </div>

                <div className="mb-6">
                  <label htmlFor="message" className="block text-white mb-2">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={5}
                    className="w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors"
                    placeholder="Your message"
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 py-3 rounded-full bg-gradient-to-r from-neon-orange to-yellow-500 text-white font-medium hover:shadow-neon transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? "Sending..." : "Send Message"}
                </button>
                </form>
              </div>
            </div>

            {/* Contact Info */}
            <div>
              <div className="glass rounded-lg p-8 border border-neon-blue/20 mb-8">
                <h2 className="text-2xl font-display font-bold text-white mb-6">
                  Connect With Us
                </h2>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="mr-4 mt-1">
                      <svg className="w-6 h-6 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold mb-1">Email</h3>
                      <p className="text-gray-400"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="mr-4 mt-1">
                      <svg className="w-6 h-6 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold mb-1">Support Hours</h3>
                      <p className="text-gray-400">Our team is available 7 days a week from 10 AM to 10 PM IST.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="mr-4 mt-1">
                      <svg className="w-6 h-6 text-neon-blue" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"></path>
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold mb-1">Discord</h3>
                      <p className="text-gray-400 mb-2">Join our Discord server for faster support and community interaction.</p>
                      <Button href="#" variant="primary" size="sm">
                        Join Discord
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="glass rounded-lg p-8 border border-neon-blue/20">
                <h2 className="text-2xl font-display font-bold text-white mb-6">
                  Frequently Asked Questions
                </h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-white font-semibold mb-2">How do I join the server?</h3>
                    <p className="text-gray-400">
                      To join our server, first join our Discord community, read the rules, and follow the instructions in the #how-to-join channel.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-white font-semibold mb-2">Is the server whitelist only?</h3>
                    <p className="text-gray-400">
                      Yes, we have a whitelist process to ensure quality roleplay. You'll need to complete a simple application in our Discord.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-white font-semibold mb-2">Do I need to speak Telugu to join?</h3>
                    <p className="text-gray-400">
                      While our server is focused on the Telugu community, we welcome everyone who respects our community and culture. Both Telugu and English are used on the server.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-white font-semibold mb-2">How can I report a player?</h3>
                    <p className="text-gray-400">
                      You can report players through our Discord in the #report-player channel or by contacting a staff member directly.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </>
  );
}
