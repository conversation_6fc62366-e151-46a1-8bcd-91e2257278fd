import React from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Image from "next/image";

export default function About() {
  return (
    <>
      <Navbar />

      {/* Header */}
      <div className="pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black">
        <div className="absolute inset-0 pattern-circuit opacity-5"></div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg">
              <span className="text-neon-orange">ABOUT</span> TGRS
            </h1>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Learn more about the Telugu Gaming Roleplay Server, our mission, and the team behind it.
            </p>
          </div>
        </div>
      </div>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* About Section */}
      <section className="py-20 relative bg-black/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="glass rounded-lg p-8 border border-neon-orange/20 mb-12">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="md:w-1/3 flex justify-center">
                <div className="relative w-32 h-32 md:w-40 md:h-40">
                  <Image
                    src="/assets/tgrs-logo.png"
                    alt="TGRS Logo"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
              <div className="md:w-2/3">
                <h2 className="text-2xl md:text-3xl font-display font-bold text-white mb-4">
                  Our Story
                </h2>
                <p className="text-gray-400 mb-4">
                  TGRS was founded with a vision to create a unique and immersive roleplay experience specifically for the Telugu gaming community. What started as a small project has grown into a vibrant community of passionate roleplayers.
                </p>
                <p className="text-gray-400 mb-4">
                  Our server is built on the principles of creativity, respect, and fun. We believe in creating an environment where players can express themselves through roleplay while building lasting friendships.
                </p>
                <p className="text-gray-400">
                  With custom scripts, unique features, and a dedicated team, TGRS offers an unparalleled gaming experience that celebrates Telugu culture and community.
                </p>
              </div>
            </div>
          </div>

          {/* Mission & Values */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <div className="glass-enhanced rounded-xl p-4 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-circuit">
              <div className="absolute inset-0 bg-black/20 rounded-xl"></div>

              <div className="relative z-10 flex flex-col h-full">
                <div className="mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container">
                  <div className="w-6 h-6 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center">
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      {/* Top particles */}
                      <div className="absolute w-1 h-1 bg-neon-orange rounded-full animate-ping" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>

                      {/* Right particles */}
                      <div className="absolute w-1 h-1 bg-orange-400 rounded-full animate-ping" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>

                      {/* Bottom particles */}
                      <div className="absolute w-1 h-1 bg-neon-orange rounded-full animate-ping" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>

                      {/* Left particles */}
                      <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>
                    </div>
                    <svg className="w-full h-full relative z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>
                <h2 className="text-base md:text-lg font-display font-bold text-white mb-2 group-hover:text-neon-orange transition-colors duration-300">
                  Our Mission
                </h2>
                <p className="text-xs md:text-sm text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow">
                  To create the premier roleplay platform for the Telugu gaming community, fostering creativity, immersion, and positive social connections. We strive to continuously improve and innovate.
                </p>
              </div>

              <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"></div>
              <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"></div>
            </div>

            <div className="glass-enhanced rounded-xl p-4 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-hexagon">
              <div className="absolute inset-0 bg-black/20 rounded-xl"></div>

              <div className="relative z-10 flex flex-col h-full">
                <div className="mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container">
                  <div className="w-6 h-6 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center">
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                      {/* Top particles */}
                      <div className="absolute w-1 h-1 bg-neon-orange rounded-full animate-ping" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>

                      {/* Right particles */}
                      <div className="absolute w-1 h-1 bg-orange-400 rounded-full animate-ping" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>

                      {/* Bottom particles */}
                      <div className="absolute w-1 h-1 bg-neon-orange rounded-full animate-ping" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>

                      {/* Left particles */}
                      <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>
                      <div className="absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>
                    </div>
                    <svg className="w-full h-full relative z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
                    </svg>
                  </div>
                </div>
                <h2 className="text-base md:text-lg font-display font-bold text-white mb-2 group-hover:text-neon-orange transition-colors duration-300">
                  Our Values
                </h2>
                <div className="text-xs md:text-sm text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow space-y-1">
                  <div>• Community & Creativity</div>
                  <div>• Quality & Respect</div>
                  <div>• Fun & Innovation</div>
                  <div>• Telugu Culture</div>
                </div>
              </div>

              <div className="absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"></div>
              <div className="absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"></div>
            </div>
          </div>


        </div>
      </section>

      <Footer />
    </>
  );
}
