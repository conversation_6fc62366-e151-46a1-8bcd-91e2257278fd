'use client';

import { useEffect, useRef } from 'react';

interface VIPShape {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  color: string;
  type: 'heart' | 'icon';
  icon?: 'medal' | 'award' | 'trophy' | 'gem' | 'crown';
  rotation: number;
  rotationSpeed: number;
}

const VIPBouncingShapes = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const shapesRef = useRef<VIPShape[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize VIP shapes
    const initShapes = () => {
      const shapes: VIPShape[] = [];

      // VIP rank icons with their respective colors
      const vipIcons: Array<{icon: 'medal' | 'award' | 'trophy' | 'gem' | 'crown', color: string}> = [
        { icon: 'medal', color: 'rgba(59, 130, 246, 0.7)' },    // Blue for VIP 1
        { icon: 'award', color: 'rgba(147, 51, 234, 0.7)' },    // Purple for VIP 2
        { icon: 'trophy', color: 'rgba(255, 193, 7, 0.7)' },    // Gold for VIP 3
        { icon: 'gem', color: 'rgba(239, 68, 68, 0.7)' },       // Red for VIP 4
        { icon: 'crown', color: 'rgba(255, 215, 0, 0.7)' },     // Golden for VIP 5
      ];

      // Add many more VIP icons for better population
      for (let set = 0; set < 8; set++) {
        vipIcons.forEach((vipIcon, index) => {
          shapes.push({
            id: set * 5 + index,
            x: Math.random() * (canvas.width - 60) + 30,
            y: Math.random() * (canvas.height - 60) + 30,
            vx: (Math.random() - 0.5) * 1.8,
            vy: (Math.random() - 0.5) * 1.8,
            radius: Math.random() * 8 + 12,
            color: vipIcon.color,
            type: 'icon',
            icon: vipIcon.icon,
            rotation: Math.random() * Math.PI * 2,
            rotationSpeed: (Math.random() - 0.5) * 0.025,
          });
        });
      }

      // Add many more red hearts
      const heartColors = [
        'rgba(239, 68, 68, 0.7)',   // Red
        'rgba(220, 38, 127, 0.7)',  // Pink-red
        'rgba(190, 24, 93, 0.6)',   // Deep pink
        'rgba(244, 63, 94, 0.7)',   // Rose
        'rgba(255, 99, 132, 0.6)',  // Light red
        'rgba(255, 20, 147, 0.6)',  // Deep pink
      ];

      for (let i = 40; i < 70; i++) {
        shapes.push({
          id: i,
          x: Math.random() * (canvas.width - 50) + 25,
          y: Math.random() * (canvas.height - 50) + 25,
          vx: (Math.random() - 0.5) * 2.2,
          vy: (Math.random() - 0.5) * 2.2,
          radius: Math.random() * 6 + 8,
          color: heartColors[Math.floor(Math.random() * heartColors.length)],
          type: 'heart',
          rotation: Math.random() * Math.PI * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.035,
        });
      }

      shapesRef.current = shapes;
    };

    initShapes();

    // Collision detection between two shapes
    const checkCollision = (shape1: VIPShape, shape2: VIPShape) => {
      const dx = shape1.x - shape2.x;
      const dy = shape1.y - shape2.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      return distance < shape1.radius + shape2.radius;
    };

    // Handle collision response
    const handleCollision = (shape1: VIPShape, shape2: VIPShape) => {
      const dx = shape1.x - shape2.x;
      const dy = shape1.y - shape2.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance === 0) return;

      const nx = dx / distance;
      const ny = dy / distance;

      const dvx = shape1.vx - shape2.vx;
      const dvy = shape1.vy - shape2.vy;
      const dvn = dvx * nx + dvy * ny;

      if (dvn > 0) return;

      const impulse = 2 * dvn / 2;

      shape1.vx -= impulse * nx;
      shape1.vy -= impulse * ny;
      shape2.vx += impulse * nx;
      shape2.vy += impulse * ny;

      shape1.rotationSpeed += (Math.random() - 0.5) * 0.02;
      shape2.rotationSpeed += (Math.random() - 0.5) * 0.02;

      const overlap = shape1.radius + shape2.radius - distance;
      const separationX = (overlap / 2) * nx;
      const separationY = (overlap / 2) * ny;

      shape1.x += separationX;
      shape1.y += separationY;
      shape2.x -= separationX;
      shape2.y -= separationY;
    };

    // Draw custom VIP icons
    const drawVIPIcon = (ctx: CanvasRenderingContext2D, shape: VIPShape) => {
      ctx.save();
      ctx.translate(shape.x, shape.y);
      ctx.rotate(shape.rotation);

      ctx.fillStyle = shape.color;
      ctx.strokeStyle = shape.color.replace(/0\.\d+/, '0.9');
      ctx.lineWidth = 2;

      const r = shape.radius;

      switch (shape.icon) {
        case 'medal':
          // Draw medal (circle with ribbon)
          ctx.beginPath();
          ctx.arc(0, 0, r * 0.8, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
          // Ribbon
          ctx.beginPath();
          ctx.moveTo(-r * 0.3, -r * 0.8);
          ctx.lineTo(-r * 0.2, -r * 1.2);
          ctx.lineTo(r * 0.2, -r * 1.2);
          ctx.lineTo(r * 0.3, -r * 0.8);
          ctx.fill();
          break;

        case 'award':
          // Draw award (star shape)
          ctx.beginPath();
          for (let i = 0; i < 5; i++) {
            const angle = (i * Math.PI * 2) / 5 - Math.PI / 2;
            const x = Math.cos(angle) * r;
            const y = Math.sin(angle) * r;
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);

            const innerAngle = ((i + 0.5) * Math.PI * 2) / 5 - Math.PI / 2;
            const innerX = Math.cos(innerAngle) * r * 0.5;
            const innerY = Math.sin(innerAngle) * r * 0.5;
            ctx.lineTo(innerX, innerY);
          }
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          break;

        case 'trophy':
          // Draw trophy (cup shape)
          ctx.beginPath();
          ctx.moveTo(-r * 0.6, -r * 0.8);
          ctx.lineTo(-r * 0.6, r * 0.2);
          ctx.quadraticCurveTo(-r * 0.6, r * 0.6, 0, r * 0.6);
          ctx.quadraticCurveTo(r * 0.6, r * 0.6, r * 0.6, r * 0.2);
          ctx.lineTo(r * 0.6, -r * 0.8);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          // Base
          ctx.fillRect(-r * 0.8, r * 0.6, r * 1.6, r * 0.3);
          break;

        case 'gem':
          // Draw diamond/gem
          ctx.beginPath();
          ctx.moveTo(0, -r);
          ctx.lineTo(r * 0.6, -r * 0.3);
          ctx.lineTo(r * 0.8, r * 0.5);
          ctx.lineTo(0, r);
          ctx.lineTo(-r * 0.8, r * 0.5);
          ctx.lineTo(-r * 0.6, -r * 0.3);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          break;

        case 'crown':
          // Draw crown with proper peaks
          ctx.beginPath();
          // Base of crown
          ctx.moveTo(-r * 0.9, r * 0.4);
          ctx.lineTo(-r * 0.9, r * 0.8);
          ctx.lineTo(r * 0.9, r * 0.8);
          ctx.lineTo(r * 0.9, r * 0.4);

          // Crown peaks
          ctx.lineTo(r * 0.6, r * 0.4);
          ctx.lineTo(r * 0.7, -r * 0.2);
          ctx.lineTo(r * 0.4, r * 0.1);
          ctx.lineTo(r * 0.2, -r * 0.6);
          ctx.lineTo(0, r * 0.1);
          ctx.lineTo(-r * 0.2, -r * 0.6);
          ctx.lineTo(-r * 0.4, r * 0.1);
          ctx.lineTo(-r * 0.7, -r * 0.2);
          ctx.lineTo(-r * 0.6, r * 0.4);

          ctx.closePath();
          ctx.fill();
          ctx.stroke();

          // Add jewels on crown
          ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
          ctx.beginPath();
          ctx.arc(0, -r * 0.3, r * 0.15, 0, Math.PI * 2);
          ctx.fill();
          ctx.beginPath();
          ctx.arc(-r * 0.4, -r * 0.1, r * 0.1, 0, Math.PI * 2);
          ctx.fill();
          ctx.beginPath();
          ctx.arc(r * 0.4, -r * 0.1, r * 0.1, 0, Math.PI * 2);
          ctx.fill();
          break;
      }

      ctx.restore();
    };

    // Draw custom heart shape
    const drawHeart = (ctx: CanvasRenderingContext2D, shape: VIPShape) => {
      ctx.save();
      ctx.translate(shape.x, shape.y);
      ctx.rotate(shape.rotation);

      ctx.fillStyle = shape.color;
      ctx.strokeStyle = shape.color.replace(/0\.\d+/, '0.9');
      ctx.lineWidth = 1.5;

      const r = shape.radius;

      // Draw proper heart shape
      ctx.beginPath();

      // Start at bottom point of heart
      ctx.moveTo(0, r * 0.8);

      // Left side of heart
      ctx.bezierCurveTo(-r * 0.8, r * 0.2, -r * 0.8, -r * 0.4, -r * 0.4, -r * 0.4);
      ctx.bezierCurveTo(-r * 0.2, -r * 0.6, 0, -r * 0.4, 0, -r * 0.1);

      // Right side of heart
      ctx.bezierCurveTo(0, -r * 0.4, r * 0.2, -r * 0.6, r * 0.4, -r * 0.4);
      ctx.bezierCurveTo(r * 0.8, -r * 0.4, r * 0.8, r * 0.2, 0, r * 0.8);

      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // Add a small highlight
      ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.beginPath();
      ctx.arc(-r * 0.25, -r * 0.2, r * 0.15, 0, Math.PI * 2);
      ctx.fill();

      ctx.restore();
    };

    // Draw shape based on type
    const drawShape = (ctx: CanvasRenderingContext2D, shape: VIPShape) => {
      switch (shape.type) {
        case 'icon':
          drawVIPIcon(ctx, shape);
          break;
        case 'heart':
          drawHeart(ctx, shape);
          break;
      }
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const shapes = shapesRef.current;

      // Update positions
      shapes.forEach(shape => {
        shape.x += shape.vx;
        shape.y += shape.vy;
        shape.rotation += shape.rotationSpeed;

        // Bounce off walls
        if (shape.x - shape.radius <= 0 || shape.x + shape.radius >= canvas.width) {
          shape.vx *= -0.98;
          shape.x = Math.max(shape.radius, Math.min(canvas.width - shape.radius, shape.x));
          shape.rotationSpeed += (Math.random() - 0.5) * 0.015;
        }
        if (shape.y - shape.radius <= 0 || shape.y + shape.radius >= canvas.height) {
          shape.vy *= -0.98;
          shape.y = Math.max(shape.radius, Math.min(canvas.height - shape.radius, shape.y));
          shape.rotationSpeed += (Math.random() - 0.5) * 0.015;
        }

        // Maintain speed
        shape.vx *= 0.9995;
        shape.vy *= 0.9995;

        const maxSpeed = 3;
        const minSpeed = 0.5;
        const speed = Math.sqrt(shape.vx * shape.vx + shape.vy * shape.vy);

        if (speed > maxSpeed) {
          shape.vx = (shape.vx / speed) * maxSpeed;
          shape.vy = (shape.vy / speed) * maxSpeed;
        } else if (speed < minSpeed && speed > 0) {
          const boost = minSpeed / speed;
          shape.vx *= boost;
          shape.vy *= boost;
        } else if (speed === 0) {
          shape.vx = (Math.random() - 0.5) * 2;
          shape.vy = (Math.random() - 0.5) * 2;
        }
      });

      // Check collisions
      for (let i = 0; i < shapes.length; i++) {
        for (let j = i + 1; j < shapes.length; j++) {
          if (checkCollision(shapes[i], shapes[j])) {
            handleCollision(shapes[i], shapes[j]);
          }
        }
      }

      // Draw all shapes
      shapes.forEach(shape => drawShape(ctx, shape));

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default VIPBouncingShapes;
