@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base colors */
  --background: #000000;
  --foreground: #ffffff;

  /* Primary colors */
  --primary-50: #eef2ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;

  /* Accent colors */
  --accent-50: #ecfdf5;
  --accent-100: #d1fae5;
  --accent-200: #a7f3d0;
  --accent-300: #6ee7b7;
  --accent-400: #34d399;
  --accent-500: #10b981;
  --accent-600: #059669;
  --accent-700: #047857;
  --accent-800: #065f46;
  --accent-900: #064e3b;

  /* Golden colors to match logo theme */
  --neon-orange: #d4af37;
  --neon-orange-light: #f4d03f;
  --neon-orange-dark: #b8860b;
  --neon-red: #cd853f;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Rajdhani', 'Orbitron', sans-serif;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 140, 66, 0.05) 0%, transparent 50%);
  background-attachment: fixed;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

/* Mobile scrollbar - thinner for better mobile experience */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
  }
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-orange);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-orange-light);
}

/* Custom scrollbar for VIP cards */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #d4af37, #f4d03f);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #f4d03f, #d4af37);
}

/* Neon text effect */
.neon-text {
  text-shadow: 0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange), 0 0 15px var(--neon-orange);
}

.neon-text-enhanced {
  text-shadow:
    0 0 5px var(--neon-orange),
    0 0 10px var(--neon-orange),
    0 0 15px var(--neon-orange),
    0 0 20px var(--neon-orange),
    0 0 35px var(--neon-orange),
    0 0 40px var(--neon-orange);
}

.neon-border {
  box-shadow: 0 0 5px var(--neon-orange), 0 0 10px var(--neon-orange);
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Geometric Patterns */
.pattern-dots {
  background-image: radial-gradient(circle, rgba(212, 175, 55, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pattern-grid {
  background-image:
    linear-gradient(rgba(212, 175, 55, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(212, 175, 55, 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
}

.pattern-hexagon {
  background-image:
    radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),
    conic-gradient(from 0deg, rgba(212, 175, 55, 0.1), transparent, rgba(212, 175, 55, 0.1));
  background-size: 60px 60px, 40px 40px;
}

.pattern-circuit {
  background-image:
    linear-gradient(45deg, rgba(212, 175, 55, 0.05) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(212, 175, 55, 0.05) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(212, 175, 55, 0.05) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(212, 175, 55, 0.05) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Footer Dots Pattern - More Visible */
.pattern-dots-footer {
  background-image: radial-gradient(circle, rgba(212, 175, 55, 0.15) 1px, transparent 1px);
  background-size: 25px 25px;
}



/* Tilted Card Styles */
.card-tilt-left {
  transform: perspective(1000px) rotateY(-1deg) rotateX(1deg);
  transition: all 0.3s ease;
}

.card-tilt-right {
  transform: perspective(1000px) rotateY(1deg) rotateX(1deg);
  transition: all 0.3s ease;
}

.card-tilt-left:hover {
  transform: perspective(1000px) rotateY(-0.5deg) rotateX(0.5deg) translateY(-5px);
}

.card-tilt-right:hover {
  transform: perspective(1000px) rotateY(0.5deg) rotateX(0.5deg) translateY(-5px);
}

/* Enhanced Glass Effects */
.glass-enhanced {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(212, 175, 55, 0.2);
  position: relative;
  overflow: hidden;
}

.glass-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.5s ease;
}

.glass-enhanced:hover::before {
  left: 100%;
}

/* Futuristic Section Dividers */
.section-divider {
  position: relative;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--neon-orange), transparent);
  margin: 0;
  z-index: 50;
}

.section-divider::before,
.section-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 10px;
  height: 10px;
  background: var(--neon-orange);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 10px var(--neon-orange);
  z-index: 51;
}

.section-divider::before {
  left: 20%;
}

.section-divider::after {
  right: 20%;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Geometric Background Elements */
.geometric-bg {
  position: relative;
  overflow: hidden;
}

.geometric-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    conic-gradient(from 0deg at 20% 20%, rgba(212, 175, 55, 0.03), transparent, rgba(212, 175, 55, 0.03)),
    conic-gradient(from 180deg at 80% 80%, rgba(205, 133, 63, 0.03), transparent, rgba(205, 133, 63, 0.03));
  animation: rotate 20s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Bouncing Shapes Animation */
.bouncing-shapes-bg {
  position: relative;
  overflow: hidden;
}

.bouncing-shapes-bg::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  width: 20px;
  height: 20px;
  background: rgba(212, 175, 55, 0.6);
  border-radius: 50%;
  animation: bounce-circle 8s linear infinite;
  pointer-events: none;
  z-index: 1;
}

.bouncing-shapes-bg::after {
  content: '';
  position: absolute;
  top: 70%;
  right: 20%;
  width: 16px;
  height: 16px;
  background: rgba(205, 133, 63, 0.5);
  transform: rotate(45deg);
  animation: bounce-square 10s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes bounce-circle {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(300px, -100px);
  }
  50% {
    transform: translate(600px, 50px);
  }
  75% {
    transform: translate(200px, 150px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes bounce-square {
  0% {
    transform: rotate(45deg) translate(0, 0);
  }
  20% {
    transform: rotate(135deg) translate(-200px, -80px);
  }
  40% {
    transform: rotate(225deg) translate(-400px, 100px);
  }
  60% {
    transform: rotate(315deg) translate(-150px, 200px);
  }
  80% {
    transform: rotate(405deg) translate(100px, 50px);
  }
  100% {
    transform: rotate(495deg) translate(0, 0);
  }
}

/* Alternative wave animation for other sections */
@keyframes wave {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.waves-bg {
  position: relative;
  overflow: hidden;
}

.waves-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent),
    linear-gradient(90deg, transparent, rgba(205, 133, 63, 0.05), transparent);
  background-size: 200% 100%, 300% 100%;
  animation: wave 8s ease-in-out infinite, wave 12s ease-in-out infinite reverse;
  pointer-events: none;
}

/* Wave Background for All Major Headings */
.wave-heading-bg {
  position: relative;
  display: block;
  overflow: hidden;
  padding: 4px 24px;
  border-radius: 8px;
  width: fit-content;
  margin: 0 auto;
}

.wave-heading-bg::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -200%;
  width: 500%;
  height: calc(100% + 4px);
  background: rgba(212, 175, 55, 0.25);
  clip-path: polygon(
    0% 50%, 3% 40%, 6% 35%, 9% 40%, 12% 50%, 15% 60%, 18% 65%, 21% 60%, 24% 50%,
    27% 40%, 30% 35%, 33% 40%, 36% 50%, 39% 60%, 42% 65%, 45% 60%, 48% 50%,
    51% 40%, 54% 35%, 57% 40%, 60% 50%, 63% 60%, 66% 65%, 69% 60%, 72% 50%,
    75% 40%, 78% 35%, 81% 40%, 84% 50%, 87% 60%, 90% 65%, 93% 60%, 96% 50%,
    99% 45%, 100% 50%, 100% 100%, 0% 100%
  );
  animation: wave-seamless 8s linear infinite;
  pointer-events: none;
  z-index: -1;
}

.wave-heading-bg::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -200%;
  width: 500%;
  height: calc(100% + 4px);
  background: rgba(205, 133, 63, 0.20);
  clip-path: polygon(
    0% 50%, 4% 60%, 8% 65%, 12% 60%, 16% 50%, 20% 40%, 24% 35%, 28% 40%, 32% 50%,
    36% 60%, 40% 65%, 44% 60%, 48% 50%, 52% 40%, 56% 35%, 60% 40%, 64% 50%,
    68% 60%, 72% 65%, 76% 60%, 80% 50%, 84% 40%, 88% 35%, 92% 40%, 96% 50%,
    100% 55%, 100% 100%, 0% 100%
  );
  animation: wave-seamless-reverse 12s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes wave-seamless {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(20%);
  }
}

@keyframes wave-seamless-reverse {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-16.666%);
  }
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Better mobile text sizing */
  .mobile-text-responsive {
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
  }

  /* Mobile padding adjustments */
  .mobile-padding-fix {
    padding: 1rem !important;
  }

  /* Mobile spacing improvements */
  .mobile-spacing-fix {
    margin-bottom: 1rem !important;
  }

  /* Mobile card improvements */
  .mobile-card-fix {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  /* Mobile grid improvements */
  .mobile-grid-fix {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Mobile button improvements */
  .mobile-button-fix {
    padding: 0.75rem 1.5rem !important;
    font-size: 0.875rem !important;
  }

  /* Mobile hero improvements */
  .mobile-hero-fix {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  /* Mobile section improvements */
  .mobile-section-fix {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

/* Hero Title Styling - Fixed Version */
.hero-title-fixed {
  position: relative;
  color: #FFD700;
  text-shadow:
    0 0 10px rgba(255, 215, 0, 0.5),
    0 0 20px rgba(255, 215, 0, 0.3),
    0 0 30px rgba(255, 215, 0, 0.2),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: golden-glow 3s ease-in-out infinite alternate;
}

.hero-title-fixed span {
  position: relative;
  display: inline-block;
  animation: subtle-float 6s ease-in-out infinite;
}

/* Shimmer animation for VIP 5 button */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes golden-glow {
  0% {
    text-shadow:
      0 0 10px rgba(255, 215, 0, 0.5),
      0 0 20px rgba(255, 215, 0, 0.3),
      0 0 30px rgba(255, 215, 0, 0.2),
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  100% {
    text-shadow:
      0 0 15px rgba(255, 215, 0, 0.8),
      0 0 25px rgba(255, 215, 0, 0.5),
      0 0 35px rgba(255, 215, 0, 0.3),
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes subtle-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  animation: gradient-x 3s ease infinite;
}

/* Enhanced Navbar Animations */
@keyframes navbar-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.1), 0 0 10px rgba(212, 175, 55, 0.05);
  }
  50% {
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.2), 0 0 20px rgba(212, 175, 55, 0.1);
  }
}

.animate-navbar-glow {
  animation: navbar-glow 4s ease-in-out infinite;
}

@keyframes logo-bounce {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
}

.animate-logo-bounce {
  animation: logo-bounce 3s ease-in-out infinite;
}

@keyframes menu-slide-down {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-menu-slide-down {
  animation: menu-slide-down 0.3s ease-out;
}

/* Shimmer Animation for Most Popular Badge */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Popular Badge Shape with Notched Corners */
.popular-badge-shape {
  clip-path: polygon(
    8px 0%,
    calc(100% - 8px) 0%,
    100% 8px,
    100% calc(100% - 8px),
    calc(100% - 8px) 100%,
    8px 100%,
    0% calc(100% - 8px),
    0% 8px
  );
  position: relative;
}

.popular-badge-shape::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
  clip-path: polygon(
    8px 0%,
    calc(100% - 8px) 0%,
    100% 8px,
    100% calc(100% - 8px),
    calc(100% - 8px) 100%,
    8px 100%,
    0% calc(100% - 8px),
    0% 8px
  );
  z-index: -1;
  animation: badge-glow 2s ease-in-out infinite;
}

@keyframes badge-glow {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}


